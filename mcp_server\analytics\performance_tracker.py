"""
Performance tracking module for TutorX-MCP.

This module tracks student performance metrics including:
- Time spent on concepts
- Accuracy rates
- Completion rates
- Learning velocity
- Difficulty progression
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import statistics
import json


class PerformanceMetricType(Enum):
    """Types of performance metrics tracked."""
    ACCURACY = "accuracy"
    TIME_SPENT = "time_spent"
    COMPLETION_RATE = "completion_rate"
    ATTEMPTS = "attempts"
    MASTERY_LEVEL = "mastery_level"
    DIFFICULTY_LEVEL = "difficulty_level"
    ENGAGEMENT_SCORE = "engagement_score"


@dataclass
class PerformanceEvent:
    """Individual performance event data."""
    student_id: str
    concept_id: str
    event_type: str  # 'start', 'complete', 'answer', 'hint_used', etc.
    timestamp: datetime
    data: Dict[str, Any] = field(default_factory=dict)
    session_id: Optional[str] = None


@dataclass
class ConceptPerformance:
    """Performance metrics for a specific concept."""
    concept_id: str
    total_time_spent: float = 0.0  # in minutes
    accuracy_rate: float = 0.0  # 0.0 to 1.0
    completion_rate: float = 0.0  # 0.0 to 1.0
    attempts_count: int = 0
    mastery_level: float = 0.0  # 0.0 to 1.0
    last_accessed: Optional[datetime] = None
    difficulty_progression: List[float] = field(default_factory=list)
    engagement_scores: List[float] = field(default_factory=list)


class PerformanceTracker:
    """
    Tracks and analyzes student performance metrics for adaptive learning.
    """
    
    def __init__(self):
        # In-memory storage for development
        # In production, this would be backed by a database
        self.student_performances: Dict[str, Dict[str, ConceptPerformance]] = {}
        self.performance_events: List[PerformanceEvent] = []
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
    
    def start_learning_session(self, student_id: str, concept_id: str, 
                             difficulty_level: float = 0.5) -> str:
        """
        Start a new learning session for a student.
        
        Args:
            student_id: Unique identifier for the student
            concept_id: Concept being learned
            difficulty_level: Initial difficulty level (0.0 to 1.0)
            
        Returns:
            session_id: Unique session identifier
        """
        session_id = f"{student_id}_{concept_id}_{int(time.time())}"
        
        self.active_sessions[session_id] = {
            'student_id': student_id,
            'concept_id': concept_id,
            'start_time': datetime.utcnow(),
            'difficulty_level': difficulty_level,
            'events': [],
            'current_accuracy': 0.0,
            'questions_answered': 0,
            'correct_answers': 0,
            'hints_used': 0
        }
        
        # Record start event
        event = PerformanceEvent(
            student_id=student_id,
            concept_id=concept_id,
            event_type='session_start',
            timestamp=datetime.utcnow(),
            session_id=session_id,
            data={'difficulty_level': difficulty_level}
        )
        self.record_event(event)
        
        return session_id
    
    def record_event(self, event: PerformanceEvent) -> None:
        """Record a performance event."""
        self.performance_events.append(event)
        
        # Update active session if applicable
        if event.session_id and event.session_id in self.active_sessions:
            session = self.active_sessions[event.session_id]
            session['events'].append(event)
            
            # Update session metrics based on event type
            if event.event_type == 'answer_submitted':
                session['questions_answered'] += 1
                if event.data.get('correct', False):
                    session['correct_answers'] += 1
                session['current_accuracy'] = (
                    session['correct_answers'] / session['questions_answered']
                    if session['questions_answered'] > 0 else 0.0
                )
            elif event.event_type == 'hint_used':
                session['hints_used'] += 1
    
    def end_learning_session(self, session_id: str) -> Dict[str, Any]:
        """
        End a learning session and update performance metrics.
        
        Args:
            session_id: Session to end
            
        Returns:
            Session summary with performance metrics
        """
        if session_id not in self.active_sessions:
            return {'error': 'Session not found'}
        
        session = self.active_sessions[session_id]
        student_id = session['student_id']
        concept_id = session['concept_id']
        
        # Calculate session metrics
        end_time = datetime.utcnow()
        duration_minutes = (end_time - session['start_time']).total_seconds() / 60
        
        # Record end event
        event = PerformanceEvent(
            student_id=student_id,
            concept_id=concept_id,
            event_type='session_end',
            timestamp=end_time,
            session_id=session_id,
            data={
                'duration_minutes': duration_minutes,
                'final_accuracy': session['current_accuracy'],
                'questions_answered': session['questions_answered'],
                'hints_used': session['hints_used']
            }
        )
        self.record_event(event)
        
        # Update concept performance
        self._update_concept_performance(student_id, concept_id, session, duration_minutes)
        
        # Create session summary
        summary = {
            'session_id': session_id,
            'student_id': student_id,
            'concept_id': concept_id,
            'duration_minutes': duration_minutes,
            'accuracy': session['current_accuracy'],
            'questions_answered': session['questions_answered'],
            'hints_used': session['hints_used'],
            'engagement_score': self._calculate_engagement_score(session, duration_minutes)
        }
        
        # Remove from active sessions
        del self.active_sessions[session_id]
        
        return summary
    
    def _update_concept_performance(self, student_id: str, concept_id: str, 
                                  session: Dict[str, Any], duration_minutes: float) -> None:
        """Update the concept performance metrics for a student."""
        if student_id not in self.student_performances:
            self.student_performances[student_id] = {}
        
        if concept_id not in self.student_performances[student_id]:
            self.student_performances[student_id][concept_id] = ConceptPerformance(
                concept_id=concept_id
            )
        
        perf = self.student_performances[student_id][concept_id]
        
        # Update metrics
        perf.total_time_spent += duration_minutes
        perf.attempts_count += 1
        perf.last_accessed = datetime.utcnow()
        
        # Update accuracy (weighted average)
        if session['questions_answered'] > 0:
            new_accuracy = session['current_accuracy']
            if perf.accuracy_rate == 0.0:
                perf.accuracy_rate = new_accuracy
            else:
                # Weighted average with more weight on recent performance
                weight = 0.3  # Weight for new session
                perf.accuracy_rate = (1 - weight) * perf.accuracy_rate + weight * new_accuracy
        
        # Update difficulty progression
        perf.difficulty_progression.append(session['difficulty_level'])
        
        # Calculate engagement score
        engagement = self._calculate_engagement_score(session, duration_minutes)
        perf.engagement_scores.append(engagement)
        
        # Update mastery level based on multiple factors
        perf.mastery_level = self._calculate_mastery_level(perf)
        
        # Update completion rate (simplified - based on session completion)
        perf.completion_rate = min(1.0, perf.completion_rate + 0.1)
    
    def _calculate_engagement_score(self, session: Dict[str, Any], 
                                  duration_minutes: float) -> float:
        """Calculate engagement score based on session activity."""
        if duration_minutes == 0:
            return 0.0
        
        # Factors for engagement
        questions_per_minute = session['questions_answered'] / duration_minutes
        hint_usage_ratio = session['hints_used'] / max(1, session['questions_answered'])
        
        # Normalize and combine factors
        # Optimal questions per minute: 1-3
        question_score = min(1.0, questions_per_minute / 2.0)
        
        # Lower hint usage indicates higher engagement
        hint_score = max(0.0, 1.0 - hint_usage_ratio)
        
        # Combine scores
        engagement = (question_score * 0.6 + hint_score * 0.4)
        return min(1.0, max(0.0, engagement))
    
    def _calculate_mastery_level(self, perf: ConceptPerformance) -> float:
        """Calculate mastery level based on performance metrics."""
        if perf.attempts_count == 0:
            return 0.0
        
        # Factors for mastery
        accuracy_factor = perf.accuracy_rate
        consistency_factor = 1.0 - (statistics.stdev(perf.engagement_scores) 
                                   if len(perf.engagement_scores) > 1 else 0.0)
        attempts_factor = min(1.0, perf.attempts_count / 5.0)  # Normalize to 5 attempts
        
        # Weighted combination
        mastery = (accuracy_factor * 0.5 + 
                  consistency_factor * 0.3 + 
                  attempts_factor * 0.2)
        
        return min(1.0, max(0.0, mastery))
    
    def get_student_performance(self, student_id: str, 
                              concept_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get performance metrics for a student.
        
        Args:
            student_id: Student identifier
            concept_id: Optional specific concept, if None returns all concepts
            
        Returns:
            Performance data
        """
        if student_id not in self.student_performances:
            return {'error': 'No performance data found for student'}
        
        student_data = self.student_performances[student_id]
        
        if concept_id:
            if concept_id not in student_data:
                return {'error': f'No performance data found for concept {concept_id}'}
            
            perf = student_data[concept_id]
            return {
                'student_id': student_id,
                'concept_id': concept_id,
                'total_time_spent': perf.total_time_spent,
                'accuracy_rate': perf.accuracy_rate,
                'completion_rate': perf.completion_rate,
                'attempts_count': perf.attempts_count,
                'mastery_level': perf.mastery_level,
                'last_accessed': perf.last_accessed.isoformat() if perf.last_accessed else None,
                'average_engagement': (statistics.mean(perf.engagement_scores) 
                                     if perf.engagement_scores else 0.0)
            }
        else:
            # Return summary for all concepts
            concepts = {}
            for cid, perf in student_data.items():
                concepts[cid] = {
                    'mastery_level': perf.mastery_level,
                    'accuracy_rate': perf.accuracy_rate,
                    'total_time_spent': perf.total_time_spent,
                    'attempts_count': perf.attempts_count
                }
            
            return {
                'student_id': student_id,
                'concepts': concepts,
                'total_concepts': len(concepts),
                'average_mastery': statistics.mean([p.mastery_level for p in student_data.values()]) 
                                 if student_data else 0.0
            }
    
    def get_learning_velocity(self, student_id: str, days: int = 7) -> Dict[str, Any]:
        """
        Calculate learning velocity over a specified period.
        
        Args:
            student_id: Student identifier
            days: Number of days to analyze
            
        Returns:
            Learning velocity metrics
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Filter events for the time period
        recent_events = [
            event for event in self.performance_events
            if (event.student_id == student_id and 
                event.timestamp >= cutoff_date)
        ]
        
        if not recent_events:
            return {'error': 'No recent activity found'}
        
        # Calculate metrics
        concepts_practiced = len(set(event.concept_id for event in recent_events))
        sessions_completed = len([e for e in recent_events if e.event_type == 'session_end'])
        total_time = sum(
            event.data.get('duration_minutes', 0) 
            for event in recent_events 
            if event.event_type == 'session_end'
        )
        
        return {
            'student_id': student_id,
            'period_days': days,
            'concepts_practiced': concepts_practiced,
            'sessions_completed': sessions_completed,
            'total_time_minutes': total_time,
            'average_session_time': total_time / sessions_completed if sessions_completed > 0 else 0,
            'concepts_per_day': concepts_practiced / days,
            'sessions_per_day': sessions_completed / days
        }

[project]
name = "tutorx-mcp"
version = "0.1.0"
description = "Educational AI Tutor MCP Server"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "mcp[cli]>=1.9.3",
    "fastapi>=0.109.0",
    "uvicorn>=0.27.0",
    "aiohttp>=3.9.0",
    "gradio>=4.19.0",
    "numpy>=1.24.0",
    "pillow>=10.0.0",
    "python-multipart>=0.0.6",
    "pydantic>=2.6.0",
    "networkx>=3.0",
    "python-dotenv>=1.0.0",
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.23.0",
    "httpx>=0.26.0",
]

[project.optional-dependencies]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.23.0",
    "httpx>=0.26.0",
    "pytest-mock>=3.12.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
asyncio_mode = "auto"

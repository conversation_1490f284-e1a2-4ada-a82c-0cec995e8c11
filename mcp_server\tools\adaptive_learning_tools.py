"""
Adaptive learning tools for TutorX-MCP.

This module provides MCP tools for the adaptive learning system including:
- Performance tracking
- Real-time adaptation
- Learning analytics
- Progress monitoring
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import sys
import os
from pathlib import Path

# Add the parent directory to the Python path
current_dir = Path(__file__).parent
parent_dir = current_dir.parent.parent
sys.path.insert(0, str(parent_dir))

# Import MCP
from mcp_server.mcp_instance import mcp

# Import adaptive learning components
from mcp_server.analytics.performance_tracker import PerformanceTracker
from mcp_server.analytics.learning_analytics import LearningAnalytics
from mcp_server.analytics.progress_monitor import ProgressMonitor
from mcp_server.algorithms.adaptive_engine import AdaptiveLearningEngine
from mcp_server.algorithms.difficulty_adjuster import DifficultyAdjuster, DifficultyAdjustmentStrategy
from mcp_server.algorithms.path_optimizer import PathOptimizer, OptimizationStrategy
from mcp_server.algorithms.mastery_detector import MasteryDetector

# Initialize components
performance_tracker = PerformanceTracker()
learning_analytics = LearningAnalytics(performance_tracker)
progress_monitor = ProgressMonitor(performance_tracker, learning_analytics)
adaptive_engine = AdaptiveLearningEngine(performance_tracker, learning_analytics)
difficulty_adjuster = DifficultyAdjuster(performance_tracker)
path_optimizer = PathOptimizer(performance_tracker, learning_analytics)
mastery_detector = MasteryDetector(performance_tracker)

# Start progress monitoring
progress_monitor.start_monitoring()


@mcp.tool()
async def start_adaptive_learning_session(student_id: str, concept_id: str, 
                                        initial_difficulty: float = 0.5) -> dict:
    """
    Start an adaptive learning session for a student.
    
    Args:
        student_id: Unique identifier for the student
        concept_id: Concept being learned
        initial_difficulty: Initial difficulty level (0.0 to 1.0)
        
    Returns:
        Session information and initial recommendations
    """
    try:
        # Start performance tracking session
        session_id = performance_tracker.start_learning_session(
            student_id, concept_id, initial_difficulty
        )
        
        # Get initial adaptation recommendations
        recommendations = await adaptive_engine.analyze_and_adapt(
            student_id, concept_id, session_id
        )
        
        return {
            "success": True,
            "session_id": session_id,
            "student_id": student_id,
            "concept_id": concept_id,
            "initial_difficulty": initial_difficulty,
            "recommendations": [
                {
                    "trigger": rec.trigger.value,
                    "action_type": rec.action_type,
                    "description": rec.description,
                    "confidence": rec.confidence,
                    "priority": rec.priority
                }
                for rec in recommendations
            ],
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def record_learning_event(student_id: str, concept_id: str, event_type: str,
                               session_id: str = None, event_data: dict = None) -> dict:
    """
    Record a learning event for performance tracking.
    
    Args:
        student_id: Student identifier
        concept_id: Concept identifier
        event_type: Type of event (e.g., 'answer_submitted', 'hint_used', 'session_end')
        session_id: Optional session identifier
        event_data: Additional event data
        
    Returns:
        Confirmation and any adaptive recommendations
    """
    try:
        from mcp_server.analytics.performance_tracker import PerformanceEvent
        
        # Create and record the event
        event = PerformanceEvent(
            student_id=student_id,
            concept_id=concept_id,
            event_type=event_type,
            timestamp=datetime.utcnow(),
            session_id=session_id,
            data=event_data or {}
        )
        
        performance_tracker.record_event(event)
        
        # Get adaptive recommendations based on the event
        recommendations = await adaptive_engine.analyze_and_adapt(
            student_id, concept_id, session_id
        )
        
        return {
            "success": True,
            "event_recorded": True,
            "recommendations": [
                {
                    "trigger": rec.trigger.value,
                    "action_type": rec.action_type,
                    "description": rec.description,
                    "confidence": rec.confidence,
                    "priority": rec.priority
                }
                for rec in recommendations
            ],
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def end_adaptive_learning_session(session_id: str) -> dict:
    """
    End an adaptive learning session and get performance summary.
    
    Args:
        session_id: Session identifier
        
    Returns:
        Session summary and performance metrics
    """
    try:
        # End the session
        session_summary = performance_tracker.end_learning_session(session_id)
        
        if 'error' in session_summary:
            return {"success": False, "error": session_summary['error']}
        
        # Get mastery assessment
        student_id = session_summary['student_id']
        concept_id = session_summary['concept_id']
        
        mastery_assessment = await mastery_detector.assess_mastery(student_id, concept_id)
        
        return {
            "success": True,
            "session_summary": session_summary,
            "mastery_assessment": {
                "mastery_level": mastery_assessment.mastery_level.value,
                "mastery_score": mastery_assessment.mastery_score,
                "confidence": mastery_assessment.confidence,
                "recommendations": mastery_assessment.recommendations
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def get_adaptive_recommendations(student_id: str, concept_id: str,
                                     session_id: str = None) -> dict:
    """
    Get real-time adaptive learning recommendations for a student.
    
    Args:
        student_id: Student identifier
        concept_id: Current concept
        session_id: Optional session identifier
        
    Returns:
        Adaptive learning recommendations
    """
    try:
        recommendations = await adaptive_engine.analyze_and_adapt(
            student_id, concept_id, session_id
        )
        
        return {
            "success": True,
            "student_id": student_id,
            "concept_id": concept_id,
            "recommendations": [
                {
                    "trigger": rec.trigger.value,
                    "action_type": rec.action_type,
                    "description": rec.description,
                    "confidence": rec.confidence,
                    "priority": rec.priority,
                    "parameters": rec.parameters
                }
                for rec in recommendations
            ],
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def apply_adaptive_recommendation(student_id: str, concept_id: str,
                                      recommendation_data: dict, session_id: str = None) -> dict:
    """
    Apply an adaptive learning recommendation.
    
    Args:
        student_id: Student identifier
        concept_id: Concept identifier
        recommendation_data: Recommendation data to apply
        session_id: Optional session identifier
        
    Returns:
        Result of applying the recommendation
    """
    try:
        from mcp_server.algorithms.adaptive_engine import AdaptationRecommendation, AdaptationTrigger
        
        # Reconstruct recommendation object
        recommendation = AdaptationRecommendation(
            trigger=AdaptationTrigger(recommendation_data['trigger']),
            confidence=recommendation_data['confidence'],
            action_type=recommendation_data['action_type'],
            description=recommendation_data['description'],
            parameters=recommendation_data.get('parameters', {}),
            priority=recommendation_data['priority']
        )
        
        # Apply the recommendation
        result = await adaptive_engine.apply_adaptation(
            student_id, concept_id, recommendation, session_id
        )
        
        return {
            "success": result["success"],
            "message": result["message"],
            "changes": result["changes"],
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def get_difficulty_recommendation(student_id: str, concept_id: str,
                                      strategy: str = "moderate") -> dict:
    """
    Get difficulty adjustment recommendation for a student and concept.
    
    Args:
        student_id: Student identifier
        concept_id: Concept identifier
        strategy: Adjustment strategy ('conservative', 'moderate', 'aggressive', 'adaptive')
        
    Returns:
        Difficulty adjustment recommendation
    """
    try:
        strategy_enum = DifficultyAdjustmentStrategy(strategy)
        recommendation = await difficulty_adjuster.get_difficulty_recommendation(
            student_id, concept_id, strategy_enum
        )
        
        return {
            "success": True,
            "student_id": student_id,
            "concept_id": concept_id,
            "current_difficulty": recommendation.current_difficulty,
            "recommended_difficulty": recommendation.recommended_difficulty,
            "adjustment_magnitude": recommendation.adjustment_magnitude,
            "confidence": recommendation.confidence,
            "reasoning": recommendation.reasoning,
            "strategy_used": recommendation.strategy_used.value,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def optimize_learning_path(student_id: str, target_concepts: list,
                               strategy: str = "adaptive", max_concepts: int = 10) -> dict:
    """
    Optimize learning path for a student based on their performance and goals.
    
    Args:
        student_id: Student identifier
        target_concepts: List of target concept IDs
        strategy: Optimization strategy ('mastery_focused', 'breadth_first', 'depth_first', 'adaptive', 'remediation')
        max_concepts: Maximum number of concepts in the path
        
    Returns:
        Optimized learning path
    """
    try:
        strategy_enum = OptimizationStrategy(strategy)
        optimized_path = await path_optimizer.optimize_learning_path(
            student_id, target_concepts, strategy_enum, max_concepts
        )
        
        return {
            "success": True,
            "student_id": optimized_path.student_id,
            "strategy_used": optimized_path.strategy_used.value,
            "total_estimated_time": optimized_path.total_estimated_time,
            "confidence_score": optimized_path.confidence_score,
            "optimization_reasoning": optimized_path.optimization_reasoning,
            "path_recommendations": [
                {
                    "concept_id": rec.concept_id,
                    "concept_name": rec.concept_name,
                    "priority_score": rec.priority_score,
                    "reasoning": rec.reasoning,
                    "estimated_time_minutes": rec.estimated_time_minutes,
                    "prerequisites_met": rec.prerequisites_met,
                    "difficulty_level": rec.difficulty_level
                }
                for rec in optimized_path.path_recommendations
            ],
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def assess_concept_mastery(student_id: str, concept_id: str) -> dict:
    """
    Perform comprehensive mastery assessment for a student and concept.

    Args:
        student_id: Student identifier
        concept_id: Concept identifier

    Returns:
        Detailed mastery assessment
    """
    try:
        assessment = await mastery_detector.assess_mastery(student_id, concept_id)

        return {
            "success": True,
            "student_id": assessment.student_id,
            "concept_id": assessment.concept_id,
            "mastery_level": assessment.mastery_level.value,
            "mastery_score": assessment.mastery_score,
            "confidence": assessment.confidence,
            "evidence": [
                {
                    "indicator": ev.indicator.value,
                    "value": ev.value,
                    "weight": ev.weight,
                    "confidence": ev.confidence,
                    "description": ev.description
                }
                for ev in assessment.evidence
            ],
            "recommendations": assessment.recommendations,
            "assessment_timestamp": assessment.assessment_timestamp.isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def get_learning_analytics(student_id: str, days: int = 30) -> dict:
    """
    Get comprehensive learning analytics for a student.

    Args:
        student_id: Student identifier
        days: Number of days to analyze

    Returns:
        Learning analytics and insights
    """
    try:
        # Get learning patterns
        patterns = learning_analytics.analyze_learning_patterns(student_id, days)

        # Get learning insights
        insights = learning_analytics.generate_learning_insights(student_id, days)

        # Get performance data
        performance_data = performance_tracker.get_student_performance(student_id)

        # Get learning velocity
        velocity_data = performance_tracker.get_learning_velocity(student_id, days)

        return {
            "success": True,
            "student_id": student_id,
            "analysis_period_days": days,
            "performance_summary": performance_data,
            "learning_velocity": velocity_data,
            "learning_patterns": [
                {
                    "pattern_type": pattern.pattern_type,
                    "confidence": pattern.confidence,
                    "description": pattern.description,
                    "recommendations": pattern.recommendations,
                    "data": pattern.data
                }
                for pattern in patterns
            ],
            "learning_insights": [
                {
                    "insight_type": insight.insight_type,
                    "severity": insight.severity,
                    "title": insight.title,
                    "description": insight.description,
                    "actionable_items": insight.actionable_items,
                    "metrics": insight.metrics
                }
                for insight in insights
            ],
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def get_progress_summary(student_id: str) -> dict:
    """
    Get comprehensive progress summary for a student.

    Args:
        student_id: Student identifier

    Returns:
        Progress summary with alerts and milestones
    """
    try:
        summary = progress_monitor.get_progress_summary(student_id)

        return {
            "success": True,
            "progress_summary": summary,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def get_student_alerts(student_id: str, unresolved_only: bool = True) -> dict:
    """
    Get alerts for a specific student.

    Args:
        student_id: Student identifier
        unresolved_only: Whether to return only unresolved alerts

    Returns:
        List of student alerts
    """
    try:
        alerts = progress_monitor.get_student_alerts(student_id, unresolved_only)

        return {
            "success": True,
            "student_id": student_id,
            "unresolved_only": unresolved_only,
            "alerts": [
                {
                    "alert_id": alert.alert_id,
                    "alert_level": alert.alert_level.value,
                    "title": alert.title,
                    "description": alert.description,
                    "timestamp": alert.timestamp.isoformat(),
                    "data": alert.data,
                    "resolved": alert.resolved
                }
                for alert in alerts
            ],
            "total_alerts": len(alerts),
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def resolve_student_alert(alert_id: str) -> dict:
    """
    Mark a student alert as resolved.

    Args:
        alert_id: Alert identifier

    Returns:
        Confirmation of alert resolution
    """
    try:
        resolved = progress_monitor.resolve_alert(alert_id)

        return {
            "success": True,
            "alert_id": alert_id,
            "resolved": resolved,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def get_next_concept_suggestions(student_id: str, current_concept_id: str,
                                     count: int = 3) -> dict:
    """
    Get suggestions for the next concepts to learn.

    Args:
        student_id: Student identifier
        current_concept_id: Current concept being learned
        count: Number of suggestions to return

    Returns:
        List of suggested next concepts
    """
    try:
        suggestions = await path_optimizer.suggest_next_concepts(
            student_id, current_concept_id, count
        )

        return {
            "success": True,
            "student_id": student_id,
            "current_concept_id": current_concept_id,
            "suggested_concepts": suggestions,
            "count": len(suggestions),
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@mcp.tool()
async def get_adaptation_history(student_id: str, days: int = 7) -> dict:
    """
    Get adaptation history for a student.

    Args:
        student_id: Student identifier
        days: Number of days to look back

    Returns:
        Adaptation history and effectiveness analysis
    """
    try:
        history = adaptive_engine.get_adaptation_history(student_id, days)
        effectiveness = adaptive_engine.get_adaptation_effectiveness(student_id)

        return {
            "success": True,
            "student_id": student_id,
            "period_days": days,
            "adaptation_history": history,
            "effectiveness_analysis": effectiveness,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

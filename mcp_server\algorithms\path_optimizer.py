"""
Learning Path Optimization Algorithm for TutorX-MCP.

This module provides intelligent learning path optimization based on student
performance, learning patterns, and pedagogical principles.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import statistics
import math

from ..analytics.performance_tracker import PerformanceTracker
from ..analytics.learning_analytics import LearningAnalytics

# Try to import concept graph, use fallback if not available
try:
    from ..resources.concept_graph import CONCEPT_GRAPH
except ImportError:
    # Fallback concept graph for basic functionality
    CONCEPT_GRAPH = {
        "algebra_basics": {"name": "Algebra Basics", "prerequisites": []},
        "linear_equations": {"name": "Linear Equations", "prerequisites": ["algebra_basics"]},
        "quadratic_equations": {"name": "Quadratic Equations", "prerequisites": ["linear_equations"]},
        "polynomial_functions": {"name": "Polynomial Functions", "prerequisites": ["quadratic_equations"]},
        "algebra_linear_equations": {"name": "Linear Equations", "prerequisites": ["algebra_basics"]},
        "geometry_triangles": {"name": "Triangles", "prerequisites": []},
        "calculus_derivatives": {"name": "Derivatives", "prerequisites": ["polynomial_functions"]},
        "fractions_addition": {"name": "Adding Fractions", "prerequisites": []}
    }


class OptimizationStrategy(Enum):
    """Strategies for path optimization."""
    MASTERY_FOCUSED = "mastery_focused"      # Focus on achieving mastery
    BREADTH_FIRST = "breadth_first"          # Cover many concepts quickly
    DEPTH_FIRST = "depth_first"              # Deep understanding of fewer concepts
    ADAPTIVE = "adaptive"                    # Strategy changes based on student
    REMEDIATION = "remediation"              # Focus on filling knowledge gaps


@dataclass
class PathRecommendation:
    """Recommendation for learning path optimization."""
    concept_id: str
    concept_name: str
    priority_score: float
    reasoning: str
    estimated_time_minutes: int
    prerequisites_met: bool
    difficulty_level: float


@dataclass
class OptimizedPath:
    """Optimized learning path."""
    student_id: str
    path_recommendations: List[PathRecommendation]
    strategy_used: OptimizationStrategy
    total_estimated_time: int
    confidence_score: float
    optimization_reasoning: str


class PathOptimizer:
    """
    Intelligent learning path optimization that adapts paths based on
    student performance, learning patterns, and pedagogical principles.
    """
    
    def __init__(self, performance_tracker: PerformanceTracker,
                 learning_analytics: LearningAnalytics):
        self.performance_tracker = performance_tracker
        self.learning_analytics = learning_analytics
        
        # Optimization parameters
        self.optimization_params = {
            'mastery_threshold': 0.8,
            'prerequisite_threshold': 0.6,
            'max_path_length': 10,
            'time_weight': 0.3,
            'difficulty_weight': 0.4,
            'interest_weight': 0.3,
            'lookahead_depth': 3
        }
    
    async def optimize_learning_path(self, student_id: str, target_concepts: List[str],
                                   strategy: OptimizationStrategy = OptimizationStrategy.ADAPTIVE,
                                   max_concepts: int = 10) -> OptimizedPath:
        """
        Optimize learning path for a student based on their performance and goals.
        
        Args:
            student_id: Student identifier
            target_concepts: List of target concept IDs
            strategy: Optimization strategy to use
            max_concepts: Maximum number of concepts in the path
            
        Returns:
            Optimized learning path
        """
        # Get student performance data
        student_data = self.performance_tracker.get_student_performance(student_id)
        if 'error' in student_data:
            return self._create_default_path(target_concepts, strategy)
        
        # Choose strategy if adaptive
        if strategy == OptimizationStrategy.ADAPTIVE:
            strategy = await self._choose_optimal_strategy(student_id, student_data)
        
        # Get all required concepts (including prerequisites)
        all_required_concepts = await self._get_required_concepts(target_concepts, student_id)
        
        # Filter out already mastered concepts
        unmastered_concepts = await self._filter_unmastered_concepts(
            all_required_concepts, student_id
        )
        
        # Generate recommendations for each concept
        recommendations = []
        for concept_id in unmastered_concepts:
            recommendation = await self._generate_concept_recommendation(
                student_id, concept_id, strategy, student_data
            )
            if recommendation:
                recommendations.append(recommendation)
        
        # Sort and select top concepts based on strategy
        optimized_recommendations = await self._select_optimal_concepts(
            recommendations, strategy, max_concepts
        )
        
        # Calculate total time and confidence
        total_time = sum(rec.estimated_time_minutes for rec in optimized_recommendations)
        confidence = await self._calculate_path_confidence(student_id, optimized_recommendations)
        
        # Generate optimization reasoning
        reasoning = await self._generate_optimization_reasoning(
            student_id, strategy, optimized_recommendations
        )
        
        return OptimizedPath(
            student_id=student_id,
            path_recommendations=optimized_recommendations,
            strategy_used=strategy,
            total_estimated_time=total_time,
            confidence_score=confidence,
            optimization_reasoning=reasoning
        )
    
    async def suggest_next_concepts(self, student_id: str, current_concept_id: str,
                                  count: int = 3) -> List[str]:
        """
        Suggest the next best concepts to learn after the current one.
        
        Args:
            student_id: Student identifier
            current_concept_id: Current concept being learned
            count: Number of suggestions to return
            
        Returns:
            List of suggested concept IDs
        """
        # Get concepts that have current concept as prerequisite
        next_concepts = []
        for concept_id, concept_data in CONCEPT_GRAPH.items():
            prerequisites = concept_data.get('prerequisites', [])
            if current_concept_id in prerequisites:
                next_concepts.append(concept_id)
        
        if not next_concepts:
            # If no direct next concepts, suggest related concepts
            next_concepts = await self._find_related_concepts(current_concept_id)
        
        # Score and rank the concepts
        scored_concepts = []
        for concept_id in next_concepts:
            score = await self._calculate_concept_priority(student_id, concept_id)
            scored_concepts.append((concept_id, score))
        
        # Sort by score and return top suggestions
        scored_concepts.sort(key=lambda x: x[1], reverse=True)
        return [concept_id for concept_id, _ in scored_concepts[:count]]
    
    async def _choose_optimal_strategy(self, student_id: str, 
                                     student_data: Dict[str, Any]) -> OptimizationStrategy:
        """Choose the optimal strategy based on student profile."""
        if 'concepts' not in student_data:
            return OptimizationStrategy.MASTERY_FOCUSED
        
        concepts = student_data['concepts']
        if not concepts:
            return OptimizationStrategy.MASTERY_FOCUSED
        
        # Analyze student patterns
        patterns = self.learning_analytics.analyze_learning_patterns(student_id)
        
        # Check for struggling learner pattern
        struggling_pattern = any(p.pattern_type == "struggling_learner" for p in patterns)
        if struggling_pattern:
            return OptimizationStrategy.REMEDIATION
        
        # Check for high performer pattern
        high_performer_pattern = any(p.pattern_type == "consistent_high_performer" for p in patterns)
        if high_performer_pattern:
            return OptimizationStrategy.BREADTH_FIRST
        
        # Check learning velocity
        velocity_data = self.performance_tracker.get_learning_velocity(student_id)
        if 'error' not in velocity_data:
            concepts_per_day = velocity_data.get('concepts_per_day', 0)
            if concepts_per_day > 1.0:
                return OptimizationStrategy.BREADTH_FIRST
            elif concepts_per_day < 0.3:
                return OptimizationStrategy.DEPTH_FIRST
        
        # Default to mastery-focused
        return OptimizationStrategy.MASTERY_FOCUSED
    
    async def _get_required_concepts(self, target_concepts: List[str], 
                                   student_id: str) -> List[str]:
        """Get all concepts required to learn the target concepts."""
        required_concepts = set()
        
        def add_prerequisites(concept_id: str, visited: Set[str]):
            if concept_id in visited or concept_id not in CONCEPT_GRAPH:
                return
            
            visited.add(concept_id)
            required_concepts.add(concept_id)
            
            # Add prerequisites recursively
            prerequisites = CONCEPT_GRAPH[concept_id].get('prerequisites', [])
            for prereq_id in prerequisites:
                add_prerequisites(prereq_id, visited)
        
        # Add all target concepts and their prerequisites
        for concept_id in target_concepts:
            add_prerequisites(concept_id, set())
        
        return list(required_concepts)
    
    async def _filter_unmastered_concepts(self, concepts: List[str], 
                                        student_id: str) -> List[str]:
        """Filter out concepts that the student has already mastered."""
        unmastered = []
        
        student_data = self.performance_tracker.get_student_performance(student_id)
        if 'error' in student_data or 'concepts' not in student_data:
            return concepts  # Return all if no performance data
        
        student_concepts = student_data['concepts']
        
        for concept_id in concepts:
            if concept_id not in student_concepts:
                unmastered.append(concept_id)
            else:
                mastery_level = student_concepts[concept_id].get('mastery_level', 0.0)
                if mastery_level < self.optimization_params['mastery_threshold']:
                    unmastered.append(concept_id)
        
        return unmastered
    
    async def _generate_concept_recommendation(self, student_id: str, concept_id: str,
                                             strategy: OptimizationStrategy,
                                             student_data: Dict[str, Any]) -> Optional[PathRecommendation]:
        """Generate a recommendation for a specific concept."""
        if concept_id not in CONCEPT_GRAPH:
            return None
        
        concept_info = CONCEPT_GRAPH[concept_id]
        
        # Check if prerequisites are met
        prerequisites_met = await self._check_prerequisites_met(student_id, concept_id)
        
        # Calculate priority score based on strategy
        priority_score = await self._calculate_concept_priority(
            student_id, concept_id, strategy
        )
        
        # Estimate time based on student profile and concept complexity
        estimated_time = await self._estimate_learning_time(student_id, concept_id)
        
        # Determine difficulty level
        difficulty_level = await self._estimate_concept_difficulty(student_id, concept_id)
        
        # Generate reasoning
        reasoning = await self._generate_concept_reasoning(
            student_id, concept_id, strategy, prerequisites_met
        )
        
        return PathRecommendation(
            concept_id=concept_id,
            concept_name=concept_info.get('name', concept_id),
            priority_score=priority_score,
            reasoning=reasoning,
            estimated_time_minutes=estimated_time,
            prerequisites_met=prerequisites_met,
            difficulty_level=difficulty_level
        )
    
    async def _check_prerequisites_met(self, student_id: str, concept_id: str) -> bool:
        """Check if all prerequisites for a concept are met."""
        if concept_id not in CONCEPT_GRAPH:
            return True
        
        prerequisites = CONCEPT_GRAPH[concept_id].get('prerequisites', [])
        if not prerequisites:
            return True
        
        student_data = self.performance_tracker.get_student_performance(student_id)
        if 'error' in student_data or 'concepts' not in student_data:
            return False  # Conservative approach
        
        student_concepts = student_data['concepts']
        threshold = self.optimization_params['prerequisite_threshold']
        
        for prereq_id in prerequisites:
            if prereq_id not in student_concepts:
                return False
            
            mastery_level = student_concepts[prereq_id].get('mastery_level', 0.0)
            if mastery_level < threshold:
                return False
        
        return True
    
    async def _calculate_concept_priority(self, student_id: str, concept_id: str,
                                        strategy: OptimizationStrategy = OptimizationStrategy.MASTERY_FOCUSED) -> float:
        """Calculate priority score for a concept based on strategy."""
        base_priority = 0.5
        
        # Get concept difficulty analysis
        difficulty_analysis = self.learning_analytics.get_concept_difficulty_analysis(concept_id)
        
        if strategy == OptimizationStrategy.MASTERY_FOCUSED:
            # Prioritize concepts with moderate difficulty and high importance
            if 'error' not in difficulty_analysis:
                difficulty_score = difficulty_analysis.get('difficulty_score', 0.5)
                # Prefer moderate difficulty (around 0.5)
                difficulty_factor = 1.0 - abs(difficulty_score - 0.5)
                base_priority += difficulty_factor * 0.3
        
        elif strategy == OptimizationStrategy.BREADTH_FIRST:
            # Prioritize easier concepts to cover more ground
            if 'error' not in difficulty_analysis:
                difficulty_score = difficulty_analysis.get('difficulty_score', 0.5)
                # Prefer easier concepts
                base_priority += (1.0 - difficulty_score) * 0.4
        
        elif strategy == OptimizationStrategy.DEPTH_FIRST:
            # Prioritize concepts with more depth and complexity
            if 'error' not in difficulty_analysis:
                difficulty_score = difficulty_analysis.get('difficulty_score', 0.5)
                # Prefer more difficult concepts
                base_priority += difficulty_score * 0.4
        
        elif strategy == OptimizationStrategy.REMEDIATION:
            # Prioritize concepts where student is struggling
            student_data = self.performance_tracker.get_student_performance(student_id, concept_id)
            if 'error' not in student_data:
                accuracy = student_data.get('accuracy_rate', 0.5)
                # Higher priority for lower accuracy
                base_priority += (1.0 - accuracy) * 0.5
        
        # Add prerequisite bonus (concepts that unlock many others)
        prerequisite_bonus = await self._calculate_prerequisite_importance(concept_id)
        base_priority += prerequisite_bonus * 0.2
        
        return min(1.0, max(0.0, base_priority))
    
    async def _calculate_prerequisite_importance(self, concept_id: str) -> float:
        """Calculate how important a concept is as a prerequisite for others."""
        dependent_count = 0
        
        for other_concept_id, concept_data in CONCEPT_GRAPH.items():
            prerequisites = concept_data.get('prerequisites', [])
            if concept_id in prerequisites:
                dependent_count += 1
        
        # Normalize to 0-1 scale (assuming max 10 dependent concepts)
        return min(1.0, dependent_count / 10.0)
    
    async def _estimate_learning_time(self, student_id: str, concept_id: str) -> int:
        """Estimate learning time for a concept based on student profile."""
        # Base time estimates by concept complexity (simplified)
        base_time = 30  # 30 minutes default
        
        # Adjust based on student's learning velocity
        velocity_data = self.performance_tracker.get_learning_velocity(student_id)
        if 'error' not in velocity_data:
            avg_session_time = velocity_data.get('average_session_time', 30)
            # Use student's typical session time as a guide
            base_time = int(avg_session_time * 1.2)  # Add 20% buffer
        
        # Adjust based on concept difficulty
        difficulty_analysis = self.learning_analytics.get_concept_difficulty_analysis(concept_id)
        if 'error' not in difficulty_analysis:
            difficulty_score = difficulty_analysis.get('difficulty_score', 0.5)
            # More difficult concepts take longer
            time_multiplier = 0.7 + (difficulty_score * 0.6)  # Range: 0.7 to 1.3
            base_time = int(base_time * time_multiplier)
        
        return max(15, min(90, base_time))  # Clamp between 15 and 90 minutes
    
    async def _estimate_concept_difficulty(self, student_id: str, concept_id: str) -> float:
        """Estimate the difficulty level of a concept for a specific student."""
        # Get global concept difficulty
        difficulty_analysis = self.learning_analytics.get_concept_difficulty_analysis(concept_id)
        if 'error' in difficulty_analysis:
            return 0.5  # Default difficulty
        
        global_difficulty = difficulty_analysis.get('difficulty_score', 0.5)
        
        # Adjust based on student's performance on related concepts
        student_data = self.performance_tracker.get_student_performance(student_id)
        if 'error' in student_data or 'concepts' not in student_data:
            return global_difficulty
        
        # Find related concepts (prerequisites and similar concepts)
        related_concepts = CONCEPT_GRAPH[concept_id].get('prerequisites', [])
        
        if related_concepts:
            student_concepts = student_data['concepts']
            related_performance = []
            
            for related_id in related_concepts:
                if related_id in student_concepts:
                    mastery = student_concepts[related_id].get('mastery_level', 0.5)
                    related_performance.append(mastery)
            
            if related_performance:
                avg_related_mastery = statistics.mean(related_performance)
                # If student performs well on related concepts, this might be easier
                difficulty_adjustment = (0.5 - avg_related_mastery) * 0.3
                return max(0.1, min(1.0, global_difficulty + difficulty_adjustment))
        
        return global_difficulty
    
    async def _generate_concept_reasoning(self, student_id: str, concept_id: str,
                                        strategy: OptimizationStrategy,
                                        prerequisites_met: bool) -> str:
        """Generate reasoning for why a concept is recommended."""
        reasons = []
        
        if not prerequisites_met:
            reasons.append("Prerequisites need to be completed first")
            return "; ".join(reasons)
        
        # Strategy-specific reasoning
        if strategy == OptimizationStrategy.MASTERY_FOCUSED:
            reasons.append("Important for building strong foundation")
        elif strategy == OptimizationStrategy.BREADTH_FIRST:
            reasons.append("Expands knowledge breadth efficiently")
        elif strategy == OptimizationStrategy.DEPTH_FIRST:
            reasons.append("Provides deep understanding of core concepts")
        elif strategy == OptimizationStrategy.REMEDIATION:
            reasons.append("Addresses identified knowledge gaps")
        
        # Add concept-specific reasoning
        difficulty_analysis = self.learning_analytics.get_concept_difficulty_analysis(concept_id)
        if 'error' not in difficulty_analysis:
            difficulty = difficulty_analysis.get('difficulty_score', 0.5)
            if difficulty < 0.3:
                reasons.append("Relatively easy to master")
            elif difficulty > 0.7:
                reasons.append("Challenging but important concept")
        
        # Add prerequisite importance
        prerequisite_importance = await self._calculate_prerequisite_importance(concept_id)
        if prerequisite_importance > 0.5:
            reasons.append("Unlocks many advanced concepts")
        
        return "; ".join(reasons) if reasons else "Recommended based on learning path analysis"
    
    async def _select_optimal_concepts(self, recommendations: List[PathRecommendation],
                                     strategy: OptimizationStrategy,
                                     max_concepts: int) -> List[PathRecommendation]:
        """Select the optimal set of concepts based on strategy."""
        # Filter out concepts without met prerequisites first
        eligible_concepts = [rec for rec in recommendations if rec.prerequisites_met]
        
        if not eligible_concepts:
            # If no concepts have prerequisites met, include some prerequisite concepts
            prerequisite_concepts = [rec for rec in recommendations if not rec.prerequisites_met]
            # Sort by priority and take top few
            prerequisite_concepts.sort(key=lambda x: x.priority_score, reverse=True)
            eligible_concepts = prerequisite_concepts[:min(3, len(prerequisite_concepts))]
        
        # Sort by priority score
        eligible_concepts.sort(key=lambda x: x.priority_score, reverse=True)
        
        # Apply strategy-specific selection
        if strategy == OptimizationStrategy.BREADTH_FIRST:
            # Prefer concepts with shorter estimated times
            eligible_concepts.sort(key=lambda x: (x.priority_score, -x.estimated_time_minutes), reverse=True)
        elif strategy == OptimizationStrategy.DEPTH_FIRST:
            # Prefer concepts with longer estimated times (more depth)
            eligible_concepts.sort(key=lambda x: (x.priority_score, x.estimated_time_minutes), reverse=True)
        
        # Select top concepts within time/count constraints
        selected_concepts = []
        total_time = 0
        max_time = 300  # 5 hours maximum
        
        for concept in eligible_concepts:
            if len(selected_concepts) >= max_concepts:
                break
            if total_time + concept.estimated_time_minutes > max_time:
                break
            
            selected_concepts.append(concept)
            total_time += concept.estimated_time_minutes
        
        return selected_concepts
    
    async def _calculate_path_confidence(self, student_id: str,
                                       recommendations: List[PathRecommendation]) -> float:
        """Calculate confidence in the optimized path."""
        if not recommendations:
            return 0.0
        
        # Base confidence on data availability
        student_data = self.performance_tracker.get_student_performance(student_id)
        data_confidence = 0.5 if 'error' in student_data else 0.8
        
        # Adjust based on prerequisite coverage
        prereq_met_ratio = sum(1 for rec in recommendations if rec.prerequisites_met) / len(recommendations)
        prereq_confidence = prereq_met_ratio
        
        # Adjust based on priority score distribution
        priority_scores = [rec.priority_score for rec in recommendations]
        avg_priority = statistics.mean(priority_scores)
        priority_confidence = avg_priority
        
        # Weighted combination
        overall_confidence = (
            data_confidence * 0.4 +
            prereq_confidence * 0.4 +
            priority_confidence * 0.2
        )
        
        return min(1.0, max(0.1, overall_confidence))
    
    async def _generate_optimization_reasoning(self, student_id: str,
                                             strategy: OptimizationStrategy,
                                             recommendations: List[PathRecommendation]) -> str:
        """Generate reasoning for the path optimization."""
        strategy_descriptions = {
            OptimizationStrategy.MASTERY_FOCUSED: "focused on achieving deep mastery",
            OptimizationStrategy.BREADTH_FIRST: "designed to cover concepts efficiently",
            OptimizationStrategy.DEPTH_FIRST: "emphasizing deep understanding",
            OptimizationStrategy.REMEDIATION: "addressing identified knowledge gaps",
            OptimizationStrategy.ADAPTIVE: "adapted to student's learning profile"
        }
        
        base_reasoning = f"Path {strategy_descriptions.get(strategy, 'optimized')} "
        
        if recommendations:
            total_time = sum(rec.estimated_time_minutes for rec in recommendations)
            hours = total_time // 60
            minutes = total_time % 60
            
            time_str = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"
            base_reasoning += f"with {len(recommendations)} concepts requiring approximately {time_str}"
            
            prereq_met = sum(1 for rec in recommendations if rec.prerequisites_met)
            if prereq_met < len(recommendations):
                base_reasoning += f". {len(recommendations) - prereq_met} concepts require prerequisite completion first"
        
        return base_reasoning
    
    def _create_default_path(self, target_concepts: List[str],
                           strategy: OptimizationStrategy) -> OptimizedPath:
        """Create a default path when no performance data is available."""
        recommendations = []
        
        for i, concept_id in enumerate(target_concepts[:5]):  # Limit to 5 concepts
            if concept_id in CONCEPT_GRAPH:
                concept_info = CONCEPT_GRAPH[concept_id]
                recommendations.append(PathRecommendation(
                    concept_id=concept_id,
                    concept_name=concept_info.get('name', concept_id),
                    priority_score=0.5,
                    reasoning="Default recommendation - no performance data available",
                    estimated_time_minutes=30,
                    prerequisites_met=True,  # Assume met for default path
                    difficulty_level=0.5
                ))
        
        return OptimizedPath(
            student_id="unknown",
            path_recommendations=recommendations,
            strategy_used=strategy,
            total_estimated_time=len(recommendations) * 30,
            confidence_score=0.3,
            optimization_reasoning="Default path created due to insufficient performance data"
        )
    
    async def _find_related_concepts(self, concept_id: str) -> List[str]:
        """Find concepts related to the given concept."""
        related = []
        
        if concept_id not in CONCEPT_GRAPH:
            return related
        
        concept_info = CONCEPT_GRAPH[concept_id]
        
        # Find concepts in the same category or with similar names
        for other_id, other_info in CONCEPT_GRAPH.items():
            if other_id == concept_id:
                continue
            
            # Simple similarity check based on name
            if any(word in other_info.get('name', '').lower() 
                   for word in concept_info.get('name', '').lower().split()):
                related.append(other_id)
        
        return related[:5]  # Return top 5 related concepts

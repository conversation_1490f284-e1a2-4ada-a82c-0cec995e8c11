"""
Learning analytics module for TutorX-MCP.

This module provides advanced analytics for learning patterns, trends,
and insights to support adaptive learning decisions.
"""

import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict, Counter
import math

from .performance_tracker import PerformanceTracker, ConceptPerformance


@dataclass
class LearningPattern:
    """Represents a detected learning pattern."""
    pattern_type: str
    confidence: float
    description: str
    recommendations: List[str]
    data: Dict[str, Any]


@dataclass
class LearningInsight:
    """Represents a learning insight or trend."""
    insight_type: str
    severity: str  # 'low', 'medium', 'high'
    title: str
    description: str
    actionable_items: List[str]
    metrics: Dict[str, Any]


class LearningAnalytics:
    """
    Advanced analytics engine for learning patterns and insights.
    """
    
    def __init__(self, performance_tracker: PerformanceTracker):
        self.performance_tracker = performance_tracker
    
    def analyze_learning_patterns(self, student_id: str, 
                                days: int = 30) -> List[LearningPattern]:
        """
        Analyze learning patterns for a student over a specified period.
        
        Args:
            student_id: Student identifier
            days: Number of days to analyze
            
        Returns:
            List of detected learning patterns
        """
        patterns = []
        
        # Get student performance data
        student_data = self.performance_tracker.get_student_performance(student_id)
        if 'error' in student_data:
            return patterns
        
        # Analyze different pattern types
        patterns.extend(self._detect_mastery_patterns(student_id, student_data))
        patterns.extend(self._detect_time_patterns(student_id, days))
        patterns.extend(self._detect_difficulty_patterns(student_id))
        patterns.extend(self._detect_engagement_patterns(student_id))
        
        return patterns
    
    def _detect_mastery_patterns(self, student_id: str, 
                               student_data: Dict[str, Any]) -> List[LearningPattern]:
        """Detect patterns in concept mastery progression."""
        patterns = []
        
        if 'concepts' not in student_data:
            return patterns
        
        concepts = student_data['concepts']
        mastery_levels = [concept['mastery_level'] for concept in concepts.values()]
        
        if not mastery_levels:
            return patterns
        
        avg_mastery = statistics.mean(mastery_levels)
        mastery_variance = statistics.variance(mastery_levels) if len(mastery_levels) > 1 else 0
        
        # Pattern: Consistent high performer
        if avg_mastery > 0.8 and mastery_variance < 0.05:
            patterns.append(LearningPattern(
                pattern_type="consistent_high_performer",
                confidence=0.9,
                description="Student consistently demonstrates high mastery across concepts",
                recommendations=[
                    "Introduce more challenging concepts",
                    "Consider accelerated learning path",
                    "Provide advanced practice materials"
                ],
                data={"average_mastery": avg_mastery, "variance": mastery_variance}
            ))
        
        # Pattern: Struggling learner
        elif avg_mastery < 0.4:
            patterns.append(LearningPattern(
                pattern_type="struggling_learner",
                confidence=0.8,
                description="Student showing difficulty with concept mastery",
                recommendations=[
                    "Reduce difficulty level",
                    "Provide additional foundational concepts",
                    "Increase practice time",
                    "Consider alternative learning approaches"
                ],
                data={"average_mastery": avg_mastery, "concepts_below_threshold": 
                      len([m for m in mastery_levels if m < 0.5])}
            ))
        
        # Pattern: Inconsistent performance
        elif mastery_variance > 0.2:
            patterns.append(LearningPattern(
                pattern_type="inconsistent_performance",
                confidence=0.7,
                description="Student shows high variance in concept mastery",
                recommendations=[
                    "Identify specific challenging concept areas",
                    "Provide targeted remediation",
                    "Review prerequisite concepts"
                ],
                data={"variance": mastery_variance, "range": max(mastery_levels) - min(mastery_levels)}
            ))
        
        return patterns
    
    def _detect_time_patterns(self, student_id: str, days: int) -> List[LearningPattern]:
        """Detect patterns in time usage and learning velocity."""
        patterns = []
        
        velocity_data = self.performance_tracker.get_learning_velocity(student_id, days)
        if 'error' in velocity_data:
            return patterns
        
        sessions_per_day = velocity_data['sessions_per_day']
        avg_session_time = velocity_data['average_session_time']
        
        # Pattern: High frequency, short sessions
        if sessions_per_day > 2 and avg_session_time < 15:
            patterns.append(LearningPattern(
                pattern_type="micro_learning",
                confidence=0.8,
                description="Student prefers frequent, short learning sessions",
                recommendations=[
                    "Design bite-sized learning modules",
                    "Provide quick review sessions",
                    "Use spaced repetition techniques"
                ],
                data={"sessions_per_day": sessions_per_day, "avg_session_time": avg_session_time}
            ))
        
        # Pattern: Low frequency, long sessions
        elif sessions_per_day < 0.5 and avg_session_time > 45:
            patterns.append(LearningPattern(
                pattern_type="intensive_learning",
                confidence=0.8,
                description="Student prefers infrequent, intensive learning sessions",
                recommendations=[
                    "Provide comprehensive learning modules",
                    "Include breaks and variety within sessions",
                    "Offer deep-dive content"
                ],
                data={"sessions_per_day": sessions_per_day, "avg_session_time": avg_session_time}
            ))
        
        # Pattern: Irregular learning schedule
        elif sessions_per_day < 0.3:
            patterns.append(LearningPattern(
                pattern_type="irregular_schedule",
                confidence=0.7,
                description="Student has irregular learning schedule",
                recommendations=[
                    "Send learning reminders",
                    "Provide flexible scheduling options",
                    "Create habit-building incentives"
                ],
                data={"sessions_per_day": sessions_per_day}
            ))
        
        return patterns
    
    def _detect_difficulty_patterns(self, student_id: str) -> List[LearningPattern]:
        """Detect patterns in difficulty progression and adaptation."""
        patterns = []
        
        if student_id not in self.performance_tracker.student_performances:
            return patterns
        
        student_concepts = self.performance_tracker.student_performances[student_id]
        
        # Analyze difficulty progression across concepts
        difficulty_progressions = []
        for concept_perf in student_concepts.values():
            if len(concept_perf.difficulty_progression) > 1:
                difficulty_progressions.extend(concept_perf.difficulty_progression)
        
        if len(difficulty_progressions) < 3:
            return patterns
        
        # Calculate difficulty trend
        difficulty_trend = self._calculate_trend(difficulty_progressions)
        
        # Pattern: Rapid difficulty increase tolerance
        if difficulty_trend > 0.1:
            patterns.append(LearningPattern(
                pattern_type="rapid_difficulty_adaptation",
                confidence=0.8,
                description="Student adapts quickly to increasing difficulty",
                recommendations=[
                    "Accelerate difficulty progression",
                    "Introduce advanced concepts earlier",
                    "Provide challenging bonus content"
                ],
                data={"difficulty_trend": difficulty_trend}
            ))
        
        # Pattern: Difficulty plateau
        elif abs(difficulty_trend) < 0.02:
            patterns.append(LearningPattern(
                pattern_type="difficulty_plateau",
                confidence=0.7,
                description="Student has reached a difficulty plateau",
                recommendations=[
                    "Introduce variety in problem types",
                    "Provide different learning approaches",
                    "Review and reinforce current level"
                ],
                data={"difficulty_trend": difficulty_trend}
            ))
        
        return patterns
    
    def _detect_engagement_patterns(self, student_id: str) -> List[LearningPattern]:
        """Detect patterns in student engagement levels."""
        patterns = []
        
        if student_id not in self.performance_tracker.student_performances:
            return patterns
        
        student_concepts = self.performance_tracker.student_performances[student_id]
        
        # Collect all engagement scores
        all_engagement_scores = []
        for concept_perf in student_concepts.values():
            all_engagement_scores.extend(concept_perf.engagement_scores)
        
        if len(all_engagement_scores) < 3:
            return patterns
        
        avg_engagement = statistics.mean(all_engagement_scores)
        engagement_trend = self._calculate_trend(all_engagement_scores)
        
        # Pattern: Declining engagement
        if engagement_trend < -0.1:
            patterns.append(LearningPattern(
                pattern_type="declining_engagement",
                confidence=0.8,
                description="Student engagement is declining over time",
                recommendations=[
                    "Introduce gamification elements",
                    "Vary learning activities",
                    "Provide motivational feedback",
                    "Check for learning fatigue"
                ],
                data={"engagement_trend": engagement_trend, "avg_engagement": avg_engagement}
            ))
        
        # Pattern: Low engagement
        elif avg_engagement < 0.4:
            patterns.append(LearningPattern(
                pattern_type="low_engagement",
                confidence=0.7,
                description="Student shows consistently low engagement",
                recommendations=[
                    "Identify preferred learning styles",
                    "Introduce interactive elements",
                    "Provide immediate feedback",
                    "Consider alternative content formats"
                ],
                data={"avg_engagement": avg_engagement}
            ))
        
        # Pattern: High engagement
        elif avg_engagement > 0.8:
            patterns.append(LearningPattern(
                pattern_type="high_engagement",
                confidence=0.9,
                description="Student shows high engagement levels",
                recommendations=[
                    "Maintain current approach",
                    "Provide additional challenging content",
                    "Consider peer mentoring opportunities"
                ],
                data={"avg_engagement": avg_engagement}
            ))
        
        return patterns
    
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate the trend (slope) of a series of values."""
        if len(values) < 2:
            return 0.0
        
        n = len(values)
        x_values = list(range(n))
        
        # Calculate linear regression slope
        x_mean = statistics.mean(x_values)
        y_mean = statistics.mean(values)
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, values))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator
    
    def generate_learning_insights(self, student_id: str, 
                                 days: int = 30) -> List[LearningInsight]:
        """
        Generate actionable learning insights for a student.
        
        Args:
            student_id: Student identifier
            days: Number of days to analyze
            
        Returns:
            List of learning insights
        """
        insights = []
        
        # Get patterns and performance data
        patterns = self.analyze_learning_patterns(student_id, days)
        student_data = self.performance_tracker.get_student_performance(student_id)
        velocity_data = self.performance_tracker.get_learning_velocity(student_id, days)
        
        # Generate insights based on patterns
        for pattern in patterns:
            if pattern.pattern_type == "struggling_learner":
                insights.append(LearningInsight(
                    insight_type="performance_concern",
                    severity="high",
                    title="Learning Difficulty Detected",
                    description=f"Student is struggling with concept mastery (avg: {pattern.data['average_mastery']:.2f})",
                    actionable_items=[
                        "Schedule one-on-one tutoring session",
                        "Review prerequisite concepts",
                        "Adjust learning path difficulty"
                    ],
                    metrics=pattern.data
                ))
            
            elif pattern.pattern_type == "declining_engagement":
                insights.append(LearningInsight(
                    insight_type="engagement_concern",
                    severity="medium",
                    title="Engagement Declining",
                    description="Student engagement has been decreasing over recent sessions",
                    actionable_items=[
                        "Introduce new learning activities",
                        "Provide motivational feedback",
                        "Check for external factors affecting learning"
                    ],
                    metrics=pattern.data
                ))
            
            elif pattern.pattern_type == "consistent_high_performer":
                insights.append(LearningInsight(
                    insight_type="advancement_opportunity",
                    severity="low",
                    title="Ready for Advanced Content",
                    description="Student consistently demonstrates high mastery and may benefit from acceleration",
                    actionable_items=[
                        "Introduce advanced concepts",
                        "Provide enrichment activities",
                        "Consider grade-level advancement"
                    ],
                    metrics=pattern.data
                ))
        
        # Generate insights based on velocity data
        if 'error' not in velocity_data:
            if velocity_data['sessions_per_day'] < 0.2:
                insights.append(LearningInsight(
                    insight_type="activity_concern",
                    severity="medium",
                    title="Low Learning Activity",
                    description="Student has very low learning activity frequency",
                    actionable_items=[
                        "Send learning reminders",
                        "Investigate barriers to learning",
                        "Provide flexible scheduling options"
                    ],
                    metrics=velocity_data
                ))
        
        return insights
    
    def get_concept_difficulty_analysis(self, concept_id: str) -> Dict[str, Any]:
        """
        Analyze difficulty patterns for a specific concept across all students.
        
        Args:
            concept_id: Concept to analyze
            
        Returns:
            Difficulty analysis data
        """
        concept_performances = []
        
        # Collect performance data for this concept across all students
        for student_id, concepts in self.performance_tracker.student_performances.items():
            if concept_id in concepts:
                perf = concepts[concept_id]
                concept_performances.append({
                    'student_id': student_id,
                    'mastery_level': perf.mastery_level,
                    'accuracy_rate': perf.accuracy_rate,
                    'attempts_count': perf.attempts_count,
                    'total_time_spent': perf.total_time_spent
                })
        
        if not concept_performances:
            return {'error': 'No performance data found for concept'}
        
        # Calculate aggregate statistics
        mastery_levels = [p['mastery_level'] for p in concept_performances]
        accuracy_rates = [p['accuracy_rate'] for p in concept_performances]
        time_spent = [p['total_time_spent'] for p in concept_performances]
        
        return {
            'concept_id': concept_id,
            'total_students': len(concept_performances),
            'average_mastery': statistics.mean(mastery_levels),
            'mastery_std_dev': statistics.stdev(mastery_levels) if len(mastery_levels) > 1 else 0,
            'average_accuracy': statistics.mean(accuracy_rates),
            'average_time_spent': statistics.mean(time_spent),
            'difficulty_score': self._calculate_concept_difficulty(mastery_levels, accuracy_rates, time_spent),
            'students_struggling': len([m for m in mastery_levels if m < 0.5]),
            'students_mastered': len([m for m in mastery_levels if m > 0.8])
        }
    
    def _calculate_concept_difficulty(self, mastery_levels: List[float], 
                                    accuracy_rates: List[float], 
                                    time_spent: List[float]) -> float:
        """Calculate a difficulty score for a concept based on student performance."""
        if not mastery_levels:
            return 0.5  # Default difficulty
        
        # Factors that indicate difficulty
        low_mastery_factor = 1.0 - statistics.mean(mastery_levels)
        low_accuracy_factor = 1.0 - statistics.mean(accuracy_rates)
        high_time_factor = min(1.0, statistics.mean(time_spent) / 60.0)  # Normalize to 1 hour
        
        # Weighted combination
        difficulty = (low_mastery_factor * 0.4 + 
                     low_accuracy_factor * 0.4 + 
                     high_time_factor * 0.2)
        
        return min(1.0, max(0.0, difficulty))

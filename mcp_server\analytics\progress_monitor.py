"""
Progress monitoring module for TutorX-MCP.

This module provides real-time progress monitoring and alerting
for adaptive learning interventions.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
import statistics

from .performance_tracker import PerformanceTracker
from .learning_analytics import LearningAnalytics, LearningInsight


class AlertLevel(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


@dataclass
class ProgressAlert:
    """Progress monitoring alert."""
    alert_id: str
    student_id: str
    alert_level: AlertLevel
    title: str
    description: str
    timestamp: datetime
    data: Dict[str, Any]
    resolved: bool = False


@dataclass
class ProgressMilestone:
    """Learning progress milestone."""
    milestone_id: str
    title: str
    description: str
    criteria: Dict[str, Any]
    reward_points: int = 0


class ProgressMonitor:
    """
    Real-time progress monitoring and alerting system.
    """
    
    def __init__(self, performance_tracker: PerformanceTracker, 
                 learning_analytics: LearningAnalytics):
        self.performance_tracker = performance_tracker
        self.learning_analytics = learning_analytics
        self.alerts: List[ProgressAlert] = []
        self.milestones: Dict[str, ProgressMilestone] = {}
        self.alert_callbacks: List[Callable] = []
        self.monitoring_active = False
        self._setup_default_milestones()
    
    def _setup_default_milestones(self):
        """Setup default learning milestones."""
        self.milestones = {
            "first_concept_mastery": ProgressMilestone(
                milestone_id="first_concept_mastery",
                title="First Concept Mastered",
                description="Successfully mastered your first concept",
                criteria={"mastery_level": 0.8, "concept_count": 1},
                reward_points=10
            ),
            "five_concepts_mastery": ProgressMilestone(
                milestone_id="five_concepts_mastery",
                title="Five Concepts Mastered",
                description="Successfully mastered five concepts",
                criteria={"mastery_level": 0.8, "concept_count": 5},
                reward_points=50
            ),
            "consistent_learner": ProgressMilestone(
                milestone_id="consistent_learner",
                title="Consistent Learner",
                description="Maintained regular learning for 7 days",
                criteria={"consecutive_days": 7, "min_sessions_per_day": 1},
                reward_points=30
            ),
            "accuracy_champion": ProgressMilestone(
                milestone_id="accuracy_champion",
                title="Accuracy Champion",
                description="Achieved 90% accuracy across multiple concepts",
                criteria={"accuracy_rate": 0.9, "concept_count": 3},
                reward_points=40
            ),
            "speed_learner": ProgressMilestone(
                milestone_id="speed_learner",
                title="Speed Learner",
                description="Completed learning sessions efficiently",
                criteria={"avg_session_time": 20, "mastery_level": 0.7},
                reward_points=25
            )
        }
    
    def start_monitoring(self, check_interval_minutes: int = 5):
        """Start real-time progress monitoring."""
        self.monitoring_active = True
        asyncio.create_task(self._monitoring_loop(check_interval_minutes))
    
    def stop_monitoring(self):
        """Stop progress monitoring."""
        self.monitoring_active = False
    
    async def _monitoring_loop(self, check_interval_minutes: int):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                await self._check_all_students()
                await asyncio.sleep(check_interval_minutes * 60)
            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _check_all_students(self):
        """Check progress for all students."""
        # Get all students with performance data
        all_students = set()
        for student_id in self.performance_tracker.student_performances.keys():
            all_students.add(student_id)
        
        for student_id in all_students:
            await self._check_student_progress(student_id)
    
    async def _check_student_progress(self, student_id: str):
        """Check progress for a specific student."""
        try:
            # Check for performance issues
            await self._check_performance_alerts(student_id)
            
            # Check for milestone achievements
            await self._check_milestone_achievements(student_id)
            
            # Check for engagement issues
            await self._check_engagement_alerts(student_id)
            
        except Exception as e:
            print(f"Error checking progress for student {student_id}: {e}")
    
    async def _check_performance_alerts(self, student_id: str):
        """Check for performance-related alerts."""
        student_data = self.performance_tracker.get_student_performance(student_id)
        if 'error' in student_data:
            return
        
        # Check for low average mastery
        if 'concepts' in student_data:
            concepts = student_data['concepts']
            if concepts:
                mastery_levels = [c['mastery_level'] for c in concepts.values()]
                avg_mastery = statistics.mean(mastery_levels)
                
                if avg_mastery < 0.3 and len(mastery_levels) >= 3:
                    await self._create_alert(
                        student_id=student_id,
                        alert_level=AlertLevel.CRITICAL,
                        title="Low Mastery Alert",
                        description=f"Student has low average mastery ({avg_mastery:.2f}) across concepts",
                        data={"average_mastery": avg_mastery, "concept_count": len(mastery_levels)}
                    )
                
                # Check for concepts with very low mastery
                struggling_concepts = [
                    cid for cid, data in concepts.items() 
                    if data['mastery_level'] < 0.2 and data['attempts_count'] >= 3
                ]
                
                if struggling_concepts:
                    await self._create_alert(
                        student_id=student_id,
                        alert_level=AlertLevel.WARNING,
                        title="Struggling with Concepts",
                        description=f"Student struggling with {len(struggling_concepts)} concepts",
                        data={"struggling_concepts": struggling_concepts}
                    )
    
    async def _check_engagement_alerts(self, student_id: str):
        """Check for engagement-related alerts."""
        velocity_data = self.performance_tracker.get_learning_velocity(student_id, days=7)
        if 'error' in velocity_data:
            return
        
        # Check for very low activity
        if velocity_data['sessions_per_day'] < 0.1:
            await self._create_alert(
                student_id=student_id,
                alert_level=AlertLevel.WARNING,
                title="Low Activity Alert",
                description="Student has very low learning activity in the past week",
                data=velocity_data
            )
        
        # Check for declining engagement pattern
        patterns = self.learning_analytics.analyze_learning_patterns(student_id, days=14)
        declining_engagement = any(
            p.pattern_type == "declining_engagement" for p in patterns
        )
        
        if declining_engagement:
            await self._create_alert(
                student_id=student_id,
                alert_level=AlertLevel.WARNING,
                title="Declining Engagement",
                description="Student engagement has been declining over recent sessions",
                data={"pattern_detected": "declining_engagement"}
            )
    
    async def _check_milestone_achievements(self, student_id: str):
        """Check for milestone achievements."""
        student_data = self.performance_tracker.get_student_performance(student_id)
        if 'error' in student_data:
            return
        
        velocity_data = self.performance_tracker.get_learning_velocity(student_id, days=30)
        
        for milestone_id, milestone in self.milestones.items():
            if await self._check_milestone_criteria(student_id, milestone, student_data, velocity_data):
                await self._award_milestone(student_id, milestone)
    
    async def _check_milestone_criteria(self, student_id: str, milestone: ProgressMilestone,
                                      student_data: Dict[str, Any], 
                                      velocity_data: Dict[str, Any]) -> bool:
        """Check if a student meets milestone criteria."""
        criteria = milestone.criteria
        
        if milestone.milestone_id == "first_concept_mastery":
            if 'concepts' in student_data:
                mastered_concepts = [
                    cid for cid, data in student_data['concepts'].items()
                    if data['mastery_level'] >= criteria['mastery_level']
                ]
                return len(mastered_concepts) >= criteria['concept_count']
        
        elif milestone.milestone_id == "five_concepts_mastery":
            if 'concepts' in student_data:
                mastered_concepts = [
                    cid for cid, data in student_data['concepts'].items()
                    if data['mastery_level'] >= criteria['mastery_level']
                ]
                return len(mastered_concepts) >= criteria['concept_count']
        
        elif milestone.milestone_id == "accuracy_champion":
            if 'concepts' in student_data:
                high_accuracy_concepts = [
                    cid for cid, data in student_data['concepts'].items()
                    if data['accuracy_rate'] >= criteria['accuracy_rate']
                ]
                return len(high_accuracy_concepts) >= criteria['concept_count']
        
        elif milestone.milestone_id == "speed_learner":
            if 'error' not in velocity_data:
                avg_session_time = velocity_data.get('average_session_time', 0)
                avg_mastery = student_data.get('average_mastery', 0)
                return (avg_session_time <= criteria['avg_session_time'] and 
                       avg_mastery >= criteria['mastery_level'])
        
        elif milestone.milestone_id == "consistent_learner":
            # This would require more detailed session tracking
            # For now, use sessions per day as a proxy
            if 'error' not in velocity_data:
                sessions_per_day = velocity_data.get('sessions_per_day', 0)
                return sessions_per_day >= criteria['min_sessions_per_day']
        
        return False
    
    async def _award_milestone(self, student_id: str, milestone: ProgressMilestone):
        """Award a milestone to a student."""
        # Check if already awarded (simple check - in production would use database)
        existing_awards = [
            alert for alert in self.alerts
            if (alert.student_id == student_id and 
                alert.title == f"Milestone: {milestone.title}")
        ]
        
        if existing_awards:
            return  # Already awarded
        
        await self._create_alert(
            student_id=student_id,
            alert_level=AlertLevel.INFO,
            title=f"Milestone: {milestone.title}",
            description=f"Congratulations! {milestone.description}",
            data={
                "milestone_id": milestone.milestone_id,
                "reward_points": milestone.reward_points,
                "achievement_type": "milestone"
            }
        )
    
    async def _create_alert(self, student_id: str, alert_level: AlertLevel,
                          title: str, description: str, data: Dict[str, Any]):
        """Create a new progress alert."""
        alert = ProgressAlert(
            alert_id=f"{student_id}_{int(datetime.utcnow().timestamp())}",
            student_id=student_id,
            alert_level=alert_level,
            title=title,
            description=description,
            timestamp=datetime.utcnow(),
            data=data
        )
        
        self.alerts.append(alert)
        
        # Notify callbacks
        for callback in self.alert_callbacks:
            try:
                await callback(alert)
            except Exception as e:
                print(f"Error in alert callback: {e}")
    
    def add_alert_callback(self, callback: Callable):
        """Add a callback function to be called when alerts are created."""
        self.alert_callbacks.append(callback)
    
    def get_student_alerts(self, student_id: str, 
                          unresolved_only: bool = True) -> List[ProgressAlert]:
        """Get alerts for a specific student."""
        student_alerts = [
            alert for alert in self.alerts
            if alert.student_id == student_id
        ]
        
        if unresolved_only:
            student_alerts = [alert for alert in student_alerts if not alert.resolved]
        
        return sorted(student_alerts, key=lambda x: x.timestamp, reverse=True)
    
    def resolve_alert(self, alert_id: str) -> bool:
        """Mark an alert as resolved."""
        for alert in self.alerts:
            if alert.alert_id == alert_id:
                alert.resolved = True
                return True
        return False
    
    def get_progress_summary(self, student_id: str) -> Dict[str, Any]:
        """Get a comprehensive progress summary for a student."""
        student_data = self.performance_tracker.get_student_performance(student_id)
        velocity_data = self.performance_tracker.get_learning_velocity(student_id, days=7)
        recent_alerts = self.get_student_alerts(student_id, unresolved_only=True)
        
        # Calculate milestone progress
        milestone_progress = {}
        for milestone_id, milestone in self.milestones.items():
            achieved = any(
                alert.data.get('milestone_id') == milestone_id
                for alert in self.alerts
                if alert.student_id == student_id
            )
            milestone_progress[milestone_id] = {
                "achieved": achieved,
                "title": milestone.title,
                "description": milestone.description,
                "reward_points": milestone.reward_points
            }
        
        return {
            "student_id": student_id,
            "performance_summary": student_data,
            "recent_activity": velocity_data,
            "active_alerts": len(recent_alerts),
            "alert_breakdown": {
                level.value: len([a for a in recent_alerts if a.alert_level == level])
                for level in AlertLevel
            },
            "milestone_progress": milestone_progress,
            "total_milestones_achieved": len([m for m in milestone_progress.values() if m["achieved"]]),
            "last_updated": datetime.utcnow().isoformat()
        }
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health metrics."""
        total_students = len(self.performance_tracker.student_performances)
        total_alerts = len(self.alerts)
        unresolved_alerts = len([a for a in self.alerts if not a.resolved])
        
        # Alert distribution
        alert_distribution = {
            level.value: len([a for a in self.alerts if a.alert_level == level])
            for level in AlertLevel
        }
        
        return {
            "monitoring_active": self.monitoring_active,
            "total_students_tracked": total_students,
            "total_alerts": total_alerts,
            "unresolved_alerts": unresolved_alerts,
            "alert_distribution": alert_distribution,
            "system_timestamp": datetime.utcnow().isoformat()
        }
